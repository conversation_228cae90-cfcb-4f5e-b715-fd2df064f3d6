"use client"

import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Heart, Bed, Bath, Square } from "lucide-react"
import { useState } from "react"
import { useEffect } from "react"
import { Skeleton } from "./ui/skeleton"

interface Property {
    id: number
    title: string
    price: string
    location: string
    beds: number
    baths: number
    sqft: number
    image: string
    type: string
}

interface PropertyCards {
    availableFrom: number
    balconies: number
    balconyFacing: string
    baths: number
    cluster: string
    facing: string
    floor: number
    furnishing: string
    heliumId: string
    location: {
        type: string
        coordinates: Array<number | number[]>
    }
    maintenance: number
    rent: number
    rooms: number
    sizeSqft: number
    sno: number
    society: string
    societyAge: string
    tenantType: string[]
    thumbUrl: string
    totalFloors: number
    totalRent: number
    _id: string
}

const mockProperties: Property[] = [
    {
        id: 1,
        title: "Modern Downtown Apartment",
        price: "$2,500/month",
        location: "Downtown District",
        beds: 2,
        baths: 2,
        sqft: 1200,
        image: "https://media.designcafe.com/wp-content/uploads/2021/12/22220836/interior-design-ideas-for-rented-home.jpg",
        type: "Apartment",
    },
    {
        id: 2,
        title: "Luxury Family Home",
        price: "$4,200/month",
        location: "Suburban Heights",
        beds: 4,
        baths: 3,
        sqft: 2800,
        image: "/placeholder.svg?height=200&width=300",
        type: "House",
    },
    {
        id: 3,
        title: "Cozy Studio Loft",
        price: "$1,800/month",
        location: "Arts Quarter",
        beds: 1,
        baths: 1,
        sqft: 650,
        image: "/placeholder.svg?height=200&width=300",
        type: "Studio",
    },
    {
        id: 4,
        title: "Waterfront Condo",
        price: "$3,500/month",
        location: "Marina Bay",
        beds: 3,
        baths: 2,
        sqft: 1800,
        image: "/placeholder.svg?height=200&width=300",
        type: "Condo",
    },
    {
        id: 5,
        title: "Garden Townhouse",
        price: "$2,900/month",
        location: "Green Valley",
        beds: 3,
        baths: 2.5,
        sqft: 1600,
        image: "/placeholder.svg?height=200&width=300",
        type: "Townhouse",
    },
    {
        id: 6,
        title: "City View Penthouse",
        price: "$5,800/month",
        location: "Financial District",
        beds: 3,
        baths: 3,
        sqft: 2200,
        image: "/placeholder.svg?height=200&width=300",
        type: "Penthouse",
    },
]

interface PropertyCardsProps {
    propertyData: PropertyCards[]
    propertyDataLoading: boolean
}

export function PropertyCardsView({ propertyData , propertyDataLoading }: PropertyCardsProps) {
    const [favorites, setFavorites] = useState<Set<string>>(new Set())
    const [properties, setProperties] = useState<PropertyCards[]>([])
    const [loading, setLoading] = useState(true)

    const toggleFavorite = (id: string) => {
        const newFavorites = new Set(favorites)
        if (newFavorites.has(id)) {
            newFavorites.delete(id)
        } else {
            newFavorites.add(id)
        }
        setFavorites(newFavorites)
    }

    useEffect(() => {
        if(!propertyData) return
        if(propertyData.length === 0) return
        setProperties(propertyData)
    }, [propertyData])

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 pb-4">
            {propertyDataLoading && (
                // TODO: 6 Skeleton card
                Array.from({ length: 6 }, (_, index) => (
                    <Card
                        key={`skelton-index-${index}`}
                        className="group overflow-hidden border hover:shadow-md transition-shadow duration-200 bg-card"
                    >

                        <div className="relative p-2">
                            <Skeleton className="h-48 w-full rounded-md" />
                        </div>
                        <CardContent className="p-3 pt-0">
                            <div className="space-y-1">
                                <Skeleton className="w-3/4 h-4 mb-2" />
                                <Skeleton className="w-1/2 h-4 mb-2" />
                                <Skeleton className="w-1/4 h-2 mb-2" />
                                <Skeleton className="w-full h-8 mb-2" />
                            </div>
                        </CardContent>
                    </Card>
                ))
            )}
            {properties.map((property) => (
                <Card
                    key={property._id}
                    className="group overflow-hidden border hover:shadow-md transition-shadow duration-200 bg-card"
                >
                    <div className="relative p-2">
                        <img src={property.thumbUrl + "?q=80&w=400" || "/placeholder.svg"} alt={property.society} className="w-full h-48 object-cover rounded-md" />
                        <Button
                            variant="ghost"
                            size="icon"
                            className="absolute top-4 right-4 h-8 w-8 bg-background/80 backdrop-blur-sm hover:bg-background"
                            onClick={() => toggleFavorite(property._id)}
                        >
                            <Heart className={`w-4 h-4 ${favorites.has(property._id) ? "fill-red-500 text-red-500" : ""}`} />
                        </Button>
                        <Badge variant="secondary" className={`absolute top-4 left-4 text-foreground text-xs border ${property.availableFrom < Date.now() ? "bg-green-500 hover:bg-green-600 text-white" : "bg-yellow-500 hover:bg-yellow-600 text-white"
                            }`}>
                            {property.availableFrom === 0 ? "Available Now" : `Available from ${new Date(property.availableFrom).toLocaleDateString()}`}
                        </Badge>
                    </div>

                    <CardContent className="p-3 pt-0">
                        <div className="space-y-1">
                            <h3 className="font-medium text-sm">{property.society}</h3>
                            <p className="text-xs text-muted-foreground">{property.cluster}</p>
                            <p className="font-semibold text-sm">{property.rent} + {"("}{property.maintenance}{") Maintenance"}</p>
                        </div>

                        <div className="flex items-center gap-4 mt-3 text-xs text-muted-foreground">
                            <div className="flex items-center gap-1">
                                <Bed className="w-3 h-3" />
                                <span>{property.rooms} BHK</span>
                            </div>
                            <div className="flex items-center gap-1">
                                <Bath className="w-3 h-3" />
                                <span>{property.baths}</span>
                            </div>
                            <div className="flex items-center gap-1">
                                <Square className="w-3 h-3" />
                                <span>{property.sizeSqft} Sqft</span>
                            </div>
                        </div>

                        <Button variant="outline" size="sm" className="w-full mt-4 h-8 text-xs bg-transparent">
                            View Details
                        </Button>
                    </CardContent>
                </Card>
            ))}
        </div>
    )
}
