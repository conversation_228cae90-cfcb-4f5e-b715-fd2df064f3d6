"use client";

import { useEffect, useRef, useState } from "react";
import { Message } from "@/components/message";
import { useScrollToBottom } from "@/components/use-scroll-to-bottom";
import { motion } from "framer-motion";
import { HeliumIcon, MasonryIcon, VercelIcon } from "@/components/icons";
import Link from "next/link";
import { useChat } from "ai/react";
import ChatWindow from "@/components/chatwindow";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { PropertyCardsView } from "@/components/property-cards";
import PropertyMap from "@/components/property-map";

export default function Home() {
  const { messages, handleSubmit, input, setInput, append, isLoading } = useChat();
  const [currentPropertyData, setCurrentPropertyData] = useState<any>(null);

  useEffect(() => {
    if (currentPropertyData) {
      console.log(currentPropertyData);
    }
  }, [currentPropertyData]);

  return (
    <div className="flex justify-center h-dvh bg-white dark:bg-zinc-950 px-2 max-w-7xl w-full mx-auto">
      <ChatWindow messages={messages} handleSubmit={handleSubmit} input={input} setInput={setInput} append={append} setCurrentPropertyData={setCurrentPropertyData} />
      <div className="w-full px-2 pt-4 flex-1 overflow-y-auto
  [&::-webkit-scrollbar]:w-1
  [&::-webkit-scrollbar-track]:rounded-full
  [&::-webkit-scrollbar-track]:bg-gray-100
  [&::-webkit-scrollbar-thumb]:rounded-full
  [&::-webkit-scrollbar-thumb]:bg-gray-300
  dark:[&::-webkit-scrollbar-track]:bg-neutral-700
  dark:[&::-webkit-scrollbar-thumb]:bg-neutral-500">
        <Tabs defaultValue="cards" className="w-full">
          <TabsList className="mb-2">
            <TabsTrigger value="cards">List</TabsTrigger>
            <TabsTrigger value="map">Map</TabsTrigger>
          </TabsList>

          <TabsContent value="cards">
            <PropertyCardsView propertyData={currentPropertyData} propertyDataLoading={isLoading} />
          </TabsContent>

          <TabsContent value="map">
            <PropertyMap/>
          </TabsContent>
        </Tabs>
      </div>
    </div>
    
  );
}
