import { getOrders, getTrackingInformation, ORDERS } from "@/components/data";
import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";
import { z } from "zod";
import { getDb } from "@/lib/mongodb"

const ListingsFilterSchema = z.object({
  society: z.string().optional().describe("Society name or residential complex name (e.g., 'Brigade Cornerstone Utopia', 'Gopalan Atlantis')"),
  cluster: z.enum(["Varthur", "Whitefield", "Channasandra", "Balagere", "Hoodi", "Marathahalli"]).describe("Cluster name or geographical region/area (e.g., 'Varthur', 'Whitefield', 'Channasandra')"),
  minRooms: z.number().int().min(1).optional().describe("Minimum number of rooms/bedrooms required"),
  maxRooms: z.number().int().min(1).optional().describe("Maximum number of rooms/bedrooms allowed"),
  baths: z.number().int().min(1).optional().describe("Number of bathrooms required"),
  balconies: z.number().int().min(0).optional().describe("Number of balconies required (0 or more)"),
  minSize: z.number().int().min(0).optional().describe("Minimum apartment size in square feet"),
  maxSize: z.number().int().min(0).optional().describe("Maximum apartment size in square feet"),
  furnishing: z.enum(["Full", "Semi", "None"]).optional().describe("Furnishing status - Full (fully furnished), Semi (semi-furnished), or None (unfurnished)"),
  floorMin: z.number().int().min(0).optional().describe("Minimum floor number (0 for ground floor)"),
  floorMax: z.number().int().min(0).optional().describe("Maximum floor number allowed"),
  facing: z.string().optional().describe("Direction the main unit faces (e.g., 'East', 'West', 'North', 'South', 'North East', 'North West')"),
  balconyFacing: z.string().optional().describe("Direction the balcony faces or view type (e.g., 'Garden', 'Pool', 'Parkway')"),
  minRent: z.number().int().min(0).optional().describe("Minimum monthly rent amount in currency units"),
  maxRent: z.number().int().min(0).optional().describe("Maximum monthly rent amount in currency units"),
  maintenanceMax: z.number().int().min(0).optional().describe("Maximum monthly maintenance charges allowed"),
  availableFrom: z.string().optional().describe("Availability date or status (e.g., 'Immediate', 'Oct 1 2025', 'Sep 15 2025')"),
  tenantType: z.enum(["All", "Family Only"]).optional().describe("Type of tenants allowed - 'All' (any tenant type) or 'Family Only' (restricted to families)"),
  sortBy: z.enum(["rent", "size", "floor", "societyAge"]).optional().describe("Field to sort results by - rent amount, apartment size, floor number, or society age"),
  sortOrder: z.enum(["asc", "desc"]).optional().describe("Sort order - 'asc' for ascending (low to high) or 'desc' for descending (high to low)")
})

type ListingsFilter = z.infer<typeof ListingsFilterSchema>

type Listing = {
  id?: string
  heliumId?: string
  society: string
  cluster?: string
  rooms: number
  baths: number
  balconies: number
  size: number
  furnishing: "Full" | "Semi" | "None"
  floor: number
  totalFloors?: number
  facing?: string
  balconyFacing?: string
  rent: number
  maintenance?: number
  totalRent?: number
  societyAge?: string
  availableFrom?: string
  tenantType?: string
  thumbUrl?: string
  location: {
    type: string
    coordinates: Array<number | number[]>
  }
}

async function fetchListings(filters: ListingsFilter): Promise<Listing[]> {
  const db = await getDb()
  const col = db.collection("listings")

  // DEBUG: Check total document count first
  const totalCount = await col.countDocuments()
  console.log("🔍 Total documents in collection:", totalCount)

  // DEBUG: Get a sample document to see the actual field structure
  const sampleDoc = await col.findOne()
  console.log("📄 Sample document structure:", JSON.stringify(sampleDoc, null, 2))

  // Build MongoDB filter
  const q: Record<string, any> = {}

  // Helper function for text search - use simple string contains
  if (filters.society) {
    q.society = { $regex: filters.society, $options: "i" }
  }

  if (filters.cluster) {
    q.cluster = { $regex: filters.cluster, $options: "i" }
  }

  if (filters.availableFrom) {
    q.availableFrom = { $regex: filters.availableFrom, $options: "i" }
  }

  // Exact matches
  if (filters.furnishing) q.furnishing = filters.furnishing
  if (filters.tenantType) q.tenantType = filters.tenantType
  if (filters.facing) q.facing = filters.facing
  if (filters.balconyFacing) q.balconyFacing = filters.balconyFacing

  console.log("🎯 Applied filters:", JSON.stringify(filters, null, 2))

  // Numeric ranges
  const addRange = (field: string, min?: number, max?: number) => {
    if (min == null && max == null) return
    q[field] = { ...(q[field] || {}), ...(min != null ? { $gte: min } : {}), ...(max != null ? { $lte: max } : {}) }
  }

  addRange("rooms", filters.minRooms, filters.maxRooms)
  if (filters.baths != null) q.baths = filters.baths
  if (filters.balconies != null) q.balconies = filters.balconies
  addRange("size", filters.minSize, filters.maxSize)
  addRange("floor", filters.floorMin, filters.floorMax)
  if (filters.minRent != null || filters.maxRent != null) addRange("rent", filters.minRent, filters.maxRent)
  if (filters.maintenanceMax != null) addRange("maintenance", undefined, filters.maintenanceMax)

  console.log("🔎 MongoDB query:", JSON.stringify(q, null, 2))

  // DEBUG: Test without filters first
  if (Object.keys(q).length === 0) {
    console.log("⚠️  No filters applied, fetching all documents...")
    const allDocs = await col.find({}).limit(5).toArray()
    console.log("📊 First 5 documents without filters:", JSON.stringify(allDocs, null, 2))
  }

  // Test each filter individually to see which one is causing issues
  for (const [key, value] of Object.entries(q)) {
    try {
      const testQuery = { [key]: value }
      const testCount = await col.countDocuments(testQuery)
      console.log(`🧪 Filter '${key}': ${JSON.stringify(value)} → ${testCount} matches`)
    } catch (error) {
      console.error(`❌ Error with filter ${key}:`, error)
    }
  }

  const docs = await col.find(q).limit(100).toArray()
  console.log("✅ Final query returned:", docs.length, "documents")

  // Normalize documents -> Listing[]
  const listings: Listing[] = docs.map((row: any) => {
    const listing = {
      id: row._id?.toString?.(),
      heliumNameOrId: row.heliumId,
      society: row.Society ?? row.society ?? "",
      cluster: row.Cluster ?? row.cluster,
      rooms: row.rooms,
      baths: Number(row.baths ?? 0),
      balconies: Number(row.balconies ?? 0),
      size: Number(row.sizeSqft ?? row.size ?? 0),
      furnishing: (row.furnishing ?? "None") as Listing["furnishing"],
      floor: Number(row.Floor ?? row.floor ?? 0),
      totalFloors: Number(row["Total Floors"] ?? row.totalFloors ?? 0) || undefined,
      facing: row.Facing ?? row.facing,
      balconyFacing: row.balconyFacing,
      rent: Number(String(row.Rent ?? row.rent ?? 0).replace(/[, ]/g, "")),
      maintenance: Number(String(row.Maintenance ?? row.maintenance ?? 0).replace(/[, ]/g, "")) || undefined,
      totalRent: Number(String(row["Total Rent"] ?? row.totalRent ?? 0).replace(/[, ]/g, "")) || undefined,
      societyAge: row["Society Age"] ?? row.societyAge,
      availableFrom: row.availableFrom,
      tenantType: row.tenantType,
      thumbUrl: row.thumbUrl,
      location: row.location,
    }

    return listing
  })

  return listings
}


export async function POST(request: Request) {
  const { messages } = await request.json();

  const stream = streamText({
    model: openai("gpt-4o"),
    system: `\
      You are Helium Homes' rental property assistant. 
Your role is to help users quickly find their dream rental property by asking the right clarifying questions and matching them with the most suitable listings from the database. 
Always sound friendly, professional, and conversational. 
Keep your responses concise and natural, never use bullet points, tables, or lists — instead, weave details into flowing sentences. 
When showing properties, highlight key aspects like society, cluster, number of rooms, rent, and furnishing in a smooth narrative. 
If information is missing, politely ask the user for it. 
Your goal is to make the property search simple and enjoyable.`,
    messages,
    maxSteps: 5,
    tools: {
      listOrders: {
        description: "list the properties",
        parameters: ListingsFilterSchema,
        execute: async function (params: ListingsFilter) {
          console.log(params);
          const listings = await fetchListings(params);
          console.log(listings);
          const orders = listings.map((listing) => ({
            id: listing.rent,
            name: listing.society,
            orderedAt: listing.availableFrom,
            image: listing.thumbUrl,
          }));
          return {
            orders,
            listings,
            filters: params,
          };
        },
      },
      viewTrackingInformation: {
        description: "view tracking information for a specific order",
        parameters: z.object({
          orderId: z.string(),
        }),
        execute: async function ({ orderId }: { orderId: string }) {
          const trackingInformation = getTrackingInformation({ orderId });
          await new Promise((resolve) => setTimeout(resolve, 500));
          return trackingInformation;
        },
      },
    },
  });

  return stream.toDataStreamResponse();
}
