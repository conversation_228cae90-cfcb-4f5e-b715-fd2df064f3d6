{"name": "ai-sdk-preview-roundtrips", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ai-sdk/openai": "^1.0.10", "@googlemaps/js-api-loader": "^1.16.10", "@hookform/resolvers": "^5.2.1", "@nanostores/react": "ai/react", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@vercel/analytics": "^1.3.1", "@vercel/kv": "^2.0.0", "ai": "^4.0.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "d3-scale": "^4.0.2", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^11.3.19", "input-otp": "^1.4.2", "lucide-react": "^0.542.0", "mongodb": "^6.19.0", "next": "14.2.5", "next-themes": "^0.4.6", "react": "^19.1.1", "react-day-picker": "^9.9.0", "react-dom": "^18", "react-hook-form": "^7.62.0", "react-markdown": "^9.0.1", "react-resizable-panels": "^3.0.5", "react-use": "^17.5.1", "recharts": "2.15.4", "remark-gfm": "^4.0.0", "shadcn": "^3.2.1", "sonner": "^1.5.0", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.23.8"}, "devDependencies": {"@types/d3-scale": "^4.0.8", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}